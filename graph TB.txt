graph TB
    %% External Interfaces
    IPC[IPC Handlers]
    DB[(Local Database<br/>books, folders, notes, media_files)]
    FS[File System<br/>Sync Directory]
    
    %% Main API Layer
    SyncAPI[sync-api.ts<br/>Public API Layer]
    
    %% Core Engine
    UnifiedEngine[unified-sync-engine.ts<br/>Core Sync Engine]
    
    %% Supporting Components
    ManifestMgr[manifest-manager.ts<br/>Manifest Management]
    ChangeDetector[change-detector.ts<br/>Change Detection]
    ConflictResolver[conflict-resolver.ts<br/>Conflict Resolution]
    FileOps[file-operations.ts<br/>File System Operations]
    AutoSync[auto-sync.ts<br/>Auto Sync Management]
    ImportHandler[import-handler.ts<br/>Import Processing]
    MediaUtils[media-utils.ts<br/>Media File Handling]
    
    %% Data Structures
    Manifest[.sync-manifest.json<br/>Central State Tracking]
    NotiFiles[.noti Files<br/>Portable Note Format]
    CoverFiles[.cover.jpg<br/>Hidden Cover Images]
    
    %% Database Hooks
    DBHooks[database-hooks.ts<br/>Change Tracking]
    
    %% Main Flow Connections
    IPC --> SyncAPI
    SyncAPI --> UnifiedEngine
    SyncAPI --> AutoSync
    
    %% Engine Connections
    UnifiedEngine --> ManifestMgr
    UnifiedEngine --> ChangeDetector
    UnifiedEngine --> ConflictResolver
    UnifiedEngine --> FileOps
    UnifiedEngine --> MediaUtils
    UnifiedEngine --> ImportHandler
    
    %% Data Access
    UnifiedEngine <--> DB
    ManifestMgr <--> DB
    ChangeDetector <--> DB
    
    %% File System Access
    FileOps <--> FS
    ManifestMgr <--> Manifest
    FileOps <--> NotiFiles
    FileOps <--> CoverFiles
    
    %% Auto Sync Flow
    DBHooks --> AutoSync
    AutoSync --> SyncAPI
    
    %% Import Flow
    ImportHandler --> ManifestMgr
    ImportHandler --> UnifiedEngine
    
    %% Media Processing
    MediaUtils <--> DB
    MediaUtils <--> NotiFiles
    
    %% Detailed Sync Process Flow
    subgraph "Sync Process Flow"
        Start([Sync Request])
        LoadManifest[Load Manifest]
        DetectChanges[Detect Changes]
        ProcessDeletions[Process Deletions]
        ImportItems[Import Items<br/>Books → Folders → Notes]
        ExportItems[Export Items<br/>Books → Folders → Notes]
        ResolveConflicts[Resolve Conflicts]
        UpdateManifest[Update Manifest]
        Cleanup[Cleanup & Finalize]
        End([Sync Complete])
        
        Start --> LoadManifest
        LoadManifest --> DetectChanges
        DetectChanges --> ProcessDeletions
        ProcessDeletions --> ImportItems
        ImportItems --> ExportItems
        ExportItems --> ResolveConflicts
        ResolveConflicts --> UpdateManifest
        UpdateManifest --> Cleanup
        Cleanup --> End
    end
    
    %% Data Structure Details
    subgraph "Key Data Structures"
        ManifestStructure[SyncManifest<br/>- version<br/>- deviceId<br/>- lastSync<br/>- items[]<br/>- deletions[]]
        
        ManifestItem[ManifestItem<br/>- id (composite)<br/>- type<br/>- name<br/>- path<br/>- hash<br/>- modified<br/>- relationships<br/>- metadata]
        
        Changes[Changes<br/>- toImport<br/>- toExport<br/>- conflicts<br/>- toDelete]
        
        NotiFormat[NotiFileData<br/>- version<br/>- type<br/>- schema<br/>- metadata<br/>- content (html/markdown/plain)<br/>- media[] (embedded)]
        
        ManifestStructure --> ManifestItem
        ChangeDetector --> Changes
        MediaUtils --> NotiFormat
    end
    
    %% File System Structure
    subgraph "File System Layout"
        SyncDir[Sync Directory]
        BooksDir[Books/]
        BookFolder[BookTitle/]
        FolderDir[FolderName/]
        NoteFile[note.noti]
        CoverFile[.cover.jpg]
        ManifestFile[.sync-manifest.json]
        
        SyncDir --> BooksDir
        SyncDir --> ManifestFile
        BooksDir --> BookFolder
        BookFolder --> FolderDir
        BookFolder --> CoverFile
        FolderDir --> NoteFile
        BookFolder --> NoteFile
        SyncDir --> NoteFile
    end
    
    %% Security & Error Handling
    subgraph "Security & Reliability"
        PathValidation[Path Validation<br/>Prevents Directory Traversal]
        AtomicOps[Atomic Operations<br/>Prevents Corruption]
        Mutex[Sync Mutex<br/>Prevents Concurrent Access]
        ErrorHandling[Comprehensive Error Handling<br/>Graceful Degradation]
        
        FileOps --> PathValidation
        FileOps --> AtomicOps
        SyncAPI --> Mutex
        UnifiedEngine --> ErrorHandling
    end
    
    %% Auto Sync Details
    subgraph "Auto Sync System"
        DBChange[Database Change]
        Debounce[Debounce Timer<br/>5 seconds]
        Interval[Interval Timer<br/>5 minutes]
        RetryLogic[Retry Logic<br/>Exponential Backoff]
        
        DBChange --> Debounce
        Interval --> AutoSync
        Debounce --> AutoSync
        AutoSync --> RetryLogic
    end
    
    %% Conflict Resolution Strategy
    subgraph "Conflict Resolution"
        TimestampCheck[Compare Timestamps]
        DeviceIDTiebreaker[Device ID Tiebreaker]
        MetadataMerge[Metadata Merging]
        TypeSpecific[Type-Specific Logic]
        
        ConflictResolver --> TimestampCheck
        TimestampCheck --> DeviceIDTiebreaker
        ConflictResolver --> MetadataMerge
        ConflictResolver --> TypeSpecific
    end
    
    %% Import/Export Flow
    subgraph "Import/Export Processing"
        IDMapping[Import ID Mapping<br/>Sync ID → Local ID]
        HierarchicalOrder[Hierarchical Processing<br/>Books → Folders → Notes]
        RelationshipTracking[Relationship Tracking<br/>Parent-Child Links]
        RenameHandling[Rename Detection<br/>& Cleanup]
        
        UnifiedEngine --> IDMapping
        UnifiedEngine --> HierarchicalOrder
        UnifiedEngine --> RelationshipTracking
        UnifiedEngine --> RenameHandling
    end
    
    %% Media Processing Flow
    subgraph "Media Processing"
        EmbedMedia[Embed Media as Base64]
        RestoreMedia[Restore from Base64]
        URLReplacement[URL Replacement<br/>Local ↔ Embedded]
        MediaValidation[Media Validation]
        
        MediaUtils --> EmbedMedia
        MediaUtils --> RestoreMedia
        MediaUtils --> URLReplacement
        MediaUtils --> MediaValidation
    end
    
    %% Styling
    classDef apiLayer fill:#e1f5fe
    classDef coreEngine fill:#f3e5f5
    classDef dataStructure fill:#e8f5e8
    classDef fileSystem fill:#fff3e0
    classDef security fill:#ffebee
    classDef autoSync fill:#f1f8e9
    
    class SyncAPI apiLayer
    class UnifiedEngine coreEngine
    class ManifestStructure,ManifestItem,Changes,NotiFormat dataStructure
    class FS,Manifest,NotiFiles,CoverFiles fileSystem
    class PathValidation,AtomicOps,Mutex,ErrorHandling security
    class AutoSync,DBChange,Debounce,Interval autoSync