# Noti Sync System - Complete Architecture and Data Flow

This document provides a comprehensive overview of the Noti application's synchronization system architecture and data flow.

## System Overview

The Noti sync system is a sophisticated bidirectional synchronization engine that maintains consistency between a local SQLite database and a file system backup. It supports hierarchical data structures (books, folders, notes) with embedded media files and provides both manual and automatic sync capabilities.

## Core Components

### 1. **sync-api.ts** - Public API Layer
- Main entry point for all sync operations
- Manages sync state and configuration
- Handles auto-sync coordination
- Implements mutex to prevent concurrent operations
- Emits progress events for UI updates

### 2. **unified-sync-engine.ts** - Core Sync Engine
- Orchestrates the entire sync process
- Handles hierarchical import/export (books → folders → notes)
- Manages ID mapping during imports
- Processes deletions, conflicts, and renames
- Coordinates with all supporting components

### 3. **manifest-manager.ts** - Manifest Management
- Manages the central `.sync-manifest.json` file
- Generates manifests from database state
- Handles path collision detection and resolution
- Tracks item relationships and metadata
- Provides manifest merging capabilities

### 4. **change-detector.ts** - Change Detection
- Compares database state with manifest state
- Identifies items to import, export, or delete
- Detects conflicts between local and remote versions
- Uses content hashing for change detection

### 5. **conflict-resolver.ts** - Conflict Resolution
- Resolves conflicts when items are modified on both sides
- Uses timestamp-based resolution with device ID tiebreaker
- Implements type-specific resolution strategies
- Supports intelligent metadata merging

### 6. **file-operations.ts** - File System Operations
- Handles all file system interactions with security validation
- Prevents directory traversal attacks
- Provides atomic file operations
- Supports both .noti and legacy .md formats
- Manages file/directory rename operations

### 7. **auto-sync.ts** - Automatic Sync Management
- Manages automatic sync scheduling and triggering
- Implements debounced sync to prevent excessive operations
- Provides retry logic with exponential backoff
- Coordinates with manual sync operations

### 8. **import-handler.ts** - Import Processing
- Handles importing external backup directories
- Detects backup types (Noti vs raw files)
- Parses directory structures and creates manifests
- Supports migration from legacy formats

### 9. **media-utils.ts** - Media File Handling
- Embeds media files as base64 in .noti files for portability
- Restores embedded media during import
- Handles media URL replacement and validation
- Creates markdown previews and plain text extraction

## Key Data Structures

### SyncManifest
```typescript
{
  version: number,
  deviceId: string,
  lastSync: string,
  items: ManifestItem[],
  deletions: DeletionRecord[]
}
```

### ManifestItem
```typescript
{
  id: string,           // Composite ID (type_id)
  type: 'book' | 'folder' | 'note',
  name: string,
  path: string,
  hash: string,
  modified: string,
  relationships?: {
    bookId?: string,
    folderId?: string,
    parentId?: string
  },
  metadata?: Record<string, any>
}
```

### NotiFileData (.noti format)
```typescript
{
  version: string,
  type: string,
  schema: string,
  metadata: {
    id: number,
    title: string,
    created_at: string,
    updated_at: string,
    // ... other note metadata
  },
  content: {
    html: string,
    markdown: string,
    plain_text: string
  },
  media: EmbeddedMedia[]
}
```

## File System Structure

```
sync-directory/
├── .sync-manifest.json          # Central manifest file
├── Books/                       # Books directory
│   ├── BookTitle1/
│   │   ├── .cover.jpg          # Hidden cover image
│   │   ├── FolderName/
│   │   │   └── note.noti       # Note in folder
│   │   └── note.noti           # Note in book root
│   └── BookTitle2/
└── standalone-note.noti         # Root level note
```

## Sync Process Flow

1. **Load Manifest**: Load existing `.sync-manifest.json`
2. **Detect Changes**: Compare database state with manifest
3. **Process Deletions**: Handle pending deletions first
4. **Import Items**: Import new/changed items hierarchically
5. **Export Items**: Export new/changed items hierarchically
6. **Resolve Conflicts**: Handle items modified on both sides
7. **Update Manifest**: Save updated manifest state
8. **Cleanup**: Clean up renamed/deleted files

## Security Features

- **Path Validation**: Prevents directory traversal attacks
- **Atomic Operations**: Prevents file corruption during operations
- **Sync Mutex**: Prevents concurrent sync operations
- **Input Validation**: Validates manifest and file structures

## Auto-Sync System

- **Database Change Detection**: Monitors database changes via hooks
- **Debounced Triggering**: 5-second debounce to prevent excessive syncs
- **Scheduled Intervals**: Regular sync intervals (default 5 minutes)
- **Retry Logic**: Exponential backoff on failures
- **Event Coordination**: Coordinates with manual sync operations

## Conflict Resolution Strategy

1. **Timestamp Comparison**: Newer modification time wins
2. **Device ID Tiebreaker**: Consistent resolution when timestamps equal
3. **Metadata Merging**: Preserves important fields from both versions
4. **Type-Specific Logic**: Custom resolution for different item types

## Media Processing

- **Embedding**: Media files embedded as base64 in .noti files
- **Restoration**: Base64 data restored to media_files table
- **URL Replacement**: Converts between local URLs and embedded references
- **Validation**: Validates embedded media data integrity

## Error Handling

- **Comprehensive Error Types**: Specific error codes for different failures
- **Graceful Degradation**: Continues operation when individual items fail
- **Rollback Capabilities**: Atomic operations allow rollback on failure
- **Progress Reporting**: Detailed progress events for UI feedback

## Mermaid Diagram

The complete system architecture is visualized in the Mermaid diagram above, showing:
- Component relationships and data flow
- Sync process workflow
- Data structure relationships
- File system layout
- Security and reliability features
- Auto-sync system details
- Conflict resolution strategy
- Import/export processing
- Media processing flow

This architecture ensures robust, secure, and efficient synchronization while maintaining data integrity and providing excellent user experience through progress reporting and conflict resolution.
